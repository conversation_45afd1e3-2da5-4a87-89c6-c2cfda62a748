<div class="p-6">
  <nav class="mb-8">
    <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
      <li>
        <.link navigate={~p"/admin/repositories"} class="hover:text-indigo-600">
          Repositories
        </.link>
      </li>
      <li>•</li>
      <li>
        <.link navigate={~p"/admin/repositories/#{@repository}"} class="hover:text-indigo-600">
          {@repository.name}
        </.link>
      </li>
      <li>•</li>
      <li class="font-medium text-slate-900">Edit</li>
    </ol>
  </nav>

  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">Edit Repository</h1>
    <p class="mt-2 text-sm text-slate-600">Update repository information and settings.</p>
  </div>

  <div class="max-w-2xl">
    <.form :let={f} for={@changeset} action={~p"/admin/repositories/#{@repository}"} method="put">
      <div class="bg-white rounded-lg shadow-sm border border-slate-200 divide-y divide-slate-200">
        <div class="p-6">
          <h2 class="text-lg font-medium text-slate-900 mb-4">Basic Information</h2>
          <div class="space-y-4">
            <div>
              <.input field={f[:name]} type="text" label="Name" />
            </div>
            <div>
              <.input field={f[:full_name]} type="text" label="Full Name" />
            </div>
            <div>
              <.input field={f[:owner]} type="text" label="Owner" />
            </div>
            <div>
              <.input field={f[:language]} type="text" label="Language" />
            </div>
            <div>
              <label class="flex items-center gap-2">
                <.input field={f[:private]} type="checkbox" />
                <span class="text-sm font-medium text-slate-700">Private Repository</span>
              </label>
            </div>
            <div>
              <label class="flex items-center gap-2">
                <.input field={f[:fork]} type="checkbox" />
                <span class="text-sm font-medium text-slate-700">Fork</span>
              </label>
            </div>
            <div>
              <label class="flex items-center gap-2">
                <.input field={f[:template]} type="checkbox" />
                <span class="text-sm font-medium text-slate-700">Template</span>
              </label>
            </div>
          </div>
        </div>

        <div class="p-6 bg-slate-50 flex items-center justify-end gap-4">
          <.link
            navigate={~p"/admin/repositories/#{@repository}"}
            class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
          >
            Cancel
          </.link>
          <button
            type="submit"
            variant="primary"
          >
            Save Changes
          </button>
        </div>
      </div>
    </.form>
  </div>
</div>
